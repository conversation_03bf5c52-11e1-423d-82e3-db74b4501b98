# CWA Admin Design System

## Overview

This document outlines the design system for the CWA Admin Dashboard, providing guidelines for consistent UI/UX across the application. The design system includes components, patterns, and best practices for building accessible, responsive, and user-friendly interfaces.

## Design Principles

### 1. **Accessibility First**
- WCAG 2.1 AA compliance
- Proper semantic markup and ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast ratios

### 2. **Mobile-First Responsive Design**
- Touch-friendly interactions (44px minimum touch targets)
- Progressive enhancement from mobile to desktop
- Flexible layouts that adapt to different screen sizes
- Optimized performance on mobile devices

### 3. **Performance & User Experience**
- Fast loading times with skeleton states
- Smooth animations and transitions
- Optimized images and lazy loading
- Progressive disclosure of information

### 4. **Consistency & Predictability**
- Consistent visual patterns and interactions
- Reusable components with clear APIs
- Standardized spacing, typography, and colors
- Predictable user flows and navigation

## Color System

### Primary Colors
- **Primary 50**: `#f0f9ff` - Light backgrounds
- **Primary 500**: `#0ea5e9` - Primary actions, links
- **Primary 600**: `#0284c7` - Primary button default
- **Primary 700**: `#0369a1` - Primary button hover

### Semantic Colors
- **Success**: `#10b981` - Success states, confirmations
- **Warning**: `#f59e0b` - Warnings, cautions
- **Error**: `#ef4444` - Errors, destructive actions
- **Info**: `#3b82f6` - Information, neutral actions

### Neutral Colors
- **Gray 50**: `#f9fafb` - Light backgrounds
- **Gray 100**: `#f3f4f6` - Card backgrounds
- **Gray 500**: `#6b7280` - Secondary text
- **Gray 900**: `#111827` - Primary text

## Typography

### Font Family
- **Primary**: Geist Sans (system fallback: -apple-system, BlinkMacSystemFont, sans-serif)
- **Monospace**: Geist Mono (system fallback: Monaco, Consolas, monospace)

### Text Scales
- **xs**: 12px / 16px line height
- **sm**: 14px / 20px line height
- **base**: 16px / 24px line height
- **lg**: 18px / 28px line height
- **xl**: 20px / 28px line height
- **2xl**: 24px / 32px line height

### Font Weights
- **Normal**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

## Spacing System

Based on 4px grid system:
- **1**: 4px
- **2**: 8px
- **3**: 12px
- **4**: 16px
- **6**: 24px
- **8**: 32px
- **12**: 48px
- **16**: 64px

## Component Library

### Core Components

#### Button (`/components/ui/Button.tsx`)
- **Variants**: primary, secondary, outline, ghost, danger, success
- **Sizes**: sm, md, lg, xl
- **States**: default, hover, focus, disabled, loading
- **Features**: Icons, full-width option, accessibility support

#### Card (`/components/ui/Card.tsx`)
- **Variants**: default, elevated, outlined, ghost
- **Padding**: none, sm, md, lg
- **Sub-components**: CardHeader, CardContent, CardFooter
- **Specialized**: StatsCard, FeatureCard, ProductCard

#### Form Components (`/components/ui/FormField.tsx`)
- **FormField**: Wrapper with label, error, and hint support
- **Input**: Enhanced input with validation states
- **Textarea**: Multi-line text input
- **Select**: Dropdown selection
- **Checkbox**: Boolean input with custom styling

#### File Upload (`/components/ui/FileUpload.tsx`)
- **Features**: Drag & drop, preview, validation, multiple files
- **Accessibility**: Keyboard navigation, screen reader support
- **Performance**: Image optimization, progress indicators

#### Image Gallery (`/components/ui/ImageGallery.tsx`)
- **Features**: Lightbox, thumbnail navigation, keyboard controls
- **Accessibility**: ARIA labels, focus management
- **Performance**: Lazy loading, optimized images

### Layout Components

#### DashboardLayout (`/components/layout/DashboardLayout.tsx`)
- **Features**: Responsive sidebar, header, main content area
- **Navigation**: Active state highlighting, mobile menu
- **Accessibility**: Skip links, proper heading hierarchy

### Loading States (`/components/ui/LoadingSkeleton.tsx`)
- **TableSkeleton**: For data tables
- **ProductDetailSkeleton**: For product detail modal
- **ProductFormSkeleton**: For form pages
- **UserCardSkeleton**: For user cards

## Form Validation (`/hooks/useFormValidation.ts`)

### Validation Rules
- **Required**: Field must have a value
- **MinLength/MaxLength**: String length validation
- **Min/Max**: Numeric range validation
- **Pattern**: Regex pattern matching
- **Custom**: Custom validation functions

### Common Patterns
- **Email**: Email format validation
- **Phone**: Phone number format
- **Price**: Positive number validation
- **Quantity**: Positive integer validation

## Responsive Design Patterns

### Breakpoints
- **sm**: 640px and up
- **md**: 768px and up
- **lg**: 1024px and up
- **xl**: 1280px and up

### Mobile Patterns
- **Navigation**: Collapsible sidebar, hamburger menu
- **Forms**: Single column layout, larger touch targets
- **Tables**: Horizontal scroll, card view alternatives
- **Modals**: Full-screen on mobile, centered on desktop

## Accessibility Guidelines

### Keyboard Navigation
- **Tab Order**: Logical tab sequence
- **Focus Indicators**: Visible focus states
- **Escape Key**: Close modals and dropdowns
- **Arrow Keys**: Navigate within components

### Screen Reader Support
- **ARIA Labels**: Descriptive labels for interactive elements
- **Live Regions**: Dynamic content announcements
- **Semantic Markup**: Proper HTML structure
- **Alt Text**: Descriptive image alternatives

### Color & Contrast
- **Minimum Contrast**: 4.5:1 for normal text, 3:1 for large text
- **Color Independence**: Information not conveyed by color alone
- **Focus Indicators**: High contrast focus outlines

## Performance Best Practices

### Image Optimization
- **Lazy Loading**: Load images as they enter viewport
- **Responsive Images**: Multiple sizes for different screens
- **Format Optimization**: WebP with fallbacks
- **Compression**: Optimized file sizes

### Code Splitting
- **Route-based**: Split by page/route
- **Component-based**: Lazy load heavy components
- **Vendor Splitting**: Separate vendor bundles

### Loading States
- **Skeleton Screens**: Show content structure while loading
- **Progressive Loading**: Load critical content first
- **Error Boundaries**: Graceful error handling

## Usage Examples

### Basic Button Usage
```tsx
import Button from '@/components/ui/Button';

<Button variant="primary" size="md">
  Save Changes
</Button>
```

### Form with Validation
```tsx
import FormField, { Input } from '@/components/ui/FormField';
import { useFormValidation } from '@/hooks/useFormValidation';

const { errors, validateField } = useFormValidation(rules);

<FormField
  label="Product Name"
  id="name"
  required
  error={errors.name}
  hint="Enter a descriptive product name"
>
  <Input
    id="name"
    name="name"
    value={formData.name}
    onChange={handleChange}
    error={!!errors.name}
  />
</FormField>
```

### Responsive Card Layout
```tsx
import Card, { CardHeader, CardContent } from '@/components/ui/Card';

<Card variant="elevated">
  <CardHeader
    title="Product Statistics"
    subtitle="Overview of product performance"
  />
  <CardContent>
    {/* Card content */}
  </CardContent>
</Card>
```

## Future Enhancements

### Planned Components
- **DataTable**: Advanced table with sorting, filtering, pagination
- **DatePicker**: Calendar-based date selection
- **Toast**: Non-blocking notifications
- **Tooltip**: Contextual help and information
- **Tabs**: Content organization and navigation
- **Accordion**: Collapsible content sections

### Design Tokens
- **CSS Custom Properties**: Centralized design values
- **Theme Support**: Light/dark mode switching
- **Brand Customization**: Easy brand color updates

### Animation System
- **Micro-interactions**: Subtle feedback animations
- **Page Transitions**: Smooth navigation
- **Loading Animations**: Engaging loading states
- **Gesture Support**: Touch-based interactions
