'use client';

import { useState, useEffect } from 'react';
import { VideoBlog } from '@/types/user';
import videoBlogService from '@/lib/api/videoBlogService';
import { toast } from 'sonner';
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline';

interface VideoBlogFormProps {
  videoBlog?: VideoBlog | null;
  onSubmit: (data: { title: string; description: string; videoUrl: string; thumbnailUrl: string; category: string; tags: string[] }) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export default function VideoBlogForm({
  videoBlog,
  onSubmit,
  onCancel,
  loading = false,
}: VideoBlogFormProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    videoUrl: '',
    thumbnailUrl: '',
    youtubeUrl: '',
    youtubeVideoId: '',
    category: 'General',
    tags: [] as string[],
    duration: '',
    isActive: true,
  });

  const [videoType, setVideoType] = useState<'youtube' | 'direct'>('direct');
  const [tagInput, setTagInput] = useState('');
  const [categories] = useState([
    'General',
    'Technology',
    'Education',
    'Entertainment',
    'Business',
    'Health',
    'Sports',
    'Travel',
    'Food',
    'Lifestyle',
  ]);

  useEffect(() => {
    if (videoBlog) {
      setFormData({
        title: videoBlog.title || '',
        description: videoBlog.description || '',
        videoUrl: videoBlog.videoUrl || '',
        thumbnailUrl: videoBlog.thumbnailUrl || '',
        youtubeUrl: videoBlog.youtubeUrl || '',
        youtubeVideoId: videoBlog.youtubeVideoId || '',
        category: videoBlog.category || 'General',
        tags: videoBlog.tags || [],
        duration: videoBlog.duration ? videoBlog.duration.toString() : '',
        isActive: videoBlog.isActive !== undefined ? videoBlog.isActive : true,
      });

      // Determine video type based on existing data
      if (videoBlog.youtubeVideoId || videoBlog.youtubeUrl) {
        setVideoType('youtube');
      } else {
        setVideoType('direct');
      }
    }
  }, [videoBlog]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleVideoTypeChange = (type: 'youtube' | 'direct') => {
    setVideoType(type);
    // Clear relevant fields when switching types
    if (type === 'youtube') {
      setFormData(prev => ({
        ...prev,
        videoUrl: '',
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        youtubeUrl: '',
        youtubeVideoId: '',
      }));
    }
  };

  const handleYouTubeUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setFormData(prev => ({ ...prev, youtubeUrl: url }));

    // Extract YouTube video ID
    if (url) {
      const videoId = videoBlogService.extractYouTubeVideoId(url);
      if (videoId) {
        setFormData(prev => ({
          ...prev,
          youtubeVideoId: videoId,
          videoUrl: url, // Set videoUrl for consistency
          thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
        }));
      }
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    if (!formData.description.trim()) {
      toast.error('Description is required');
      return;
    }

    if (videoType === 'youtube') {
      if (!formData.youtubeUrl.trim()) {
        toast.error('YouTube URL is required');
        return;
      }
      if (!videoBlogService.isValidYouTubeUrl(formData.youtubeUrl)) {
        toast.error('Please enter a valid YouTube URL');
        return;
      }
      if (!formData.youtubeVideoId?.trim()) {
        toast.error('YouTube Video ID is required');
        return;
      }
      // Validate YouTube video ID format (11 characters)
      if (!/^[a-zA-Z0-9_-]{11}$/.test(formData.youtubeVideoId)) {
        toast.error('YouTube Video ID must be exactly 11 characters long and contain only letters, numbers, hyphens, and underscores');
        return;
      }
    } else {
      if (!formData.videoUrl.trim()) {
        toast.error('Video URL is required');
        return;
      }
    }

    if (!formData.thumbnailUrl.trim()) {
      toast.error('Thumbnail URL is required');
      return;
    }

    // Prepare submission data
    const submitData: {
      title: string;
      description: string;
      videoUrl: string;
      thumbnailUrl: string;
      category: string;
      tags: string[];
      isActive: boolean;
      youtubeUrl?: string;
      youtubeVideoId?: string;
      duration?: number;
    } = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      videoUrl: videoType === 'youtube' ? formData.youtubeUrl : formData.videoUrl,
      thumbnailUrl: formData.thumbnailUrl.trim(),
      category: formData.category,
      tags: formData.tags,
      isActive: formData.isActive,
    };

    // Add YouTube-specific fields
    if (videoType === 'youtube') {
      submitData.youtubeUrl = formData.youtubeUrl.trim();
      submitData.youtubeVideoId = formData.youtubeVideoId?.trim();
    }

    // Add duration if provided
    if (formData.duration) {
      const duration = parseInt(formData.duration);
      if (!isNaN(duration) && duration > 0) {
        submitData.duration = duration;
      }
    }

    try {
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div className="grid grid-cols-1 gap-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              required
              maxLength={200}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter video blog title"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              rows={4}
              maxLength={1000}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter video blog description"
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Video Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Video Information</h3>
        
        {/* Video Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">Video Type</label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="videoType"
                value="direct"
                checked={videoType === 'direct'}
                onChange={() => handleVideoTypeChange('direct')}
                className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">Direct URL</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="videoType"
                value="youtube"
                checked={videoType === 'youtube'}
                onChange={() => handleVideoTypeChange('youtube')}
                className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">YouTube</span>
            </label>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {videoType === 'youtube' ? (
            <>
              <div>
                <label htmlFor="youtubeUrl" className="block text-sm font-medium text-gray-700">
                  YouTube URL *
                </label>
                <input
                  type="url"
                  id="youtubeUrl"
                  name="youtubeUrl"
                  value={formData.youtubeUrl}
                  onChange={handleYouTubeUrlChange}
                  required={videoType === 'youtube'}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://www.youtube.com/watch?v=..."
                />
                {formData.youtubeVideoId && (
                  <p className="mt-1 text-sm text-green-600">
                    Video ID extracted: {formData.youtubeVideoId}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="youtubeVideoId" className="block text-sm font-medium text-gray-700">
                  YouTube Video ID *
                </label>
                <input
                  type="text"
                  id="youtubeVideoId"
                  name="youtubeVideoId"
                  value={formData.youtubeVideoId || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, youtubeVideoId: e.target.value }))}
                  required={videoType === 'youtube'}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., dQw4w9WgXcQ"
                  pattern="[a-zA-Z0-9_-]{11}"
                  title="YouTube video ID should be 11 characters long"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Enter the 11-character YouTube video ID (e.g., from https://youtube.com/watch?v=<strong>dQw4w9WgXcQ</strong>)
                </p>
              </div>
            </>
          ) : (
            <div>
              <label htmlFor="videoUrl" className="block text-sm font-medium text-gray-700">
                Video URL *
              </label>
              <input
                type="url"
                id="videoUrl"
                name="videoUrl"
                value={formData.videoUrl}
                onChange={handleInputChange}
                required={videoType === 'direct'}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com/video.mp4"
              />
            </div>
          )}

          <div>
            <label htmlFor="thumbnailUrl" className="block text-sm font-medium text-gray-700">
              Thumbnail URL *
            </label>
            <input
              type="url"
              id="thumbnailUrl"
              name="thumbnailUrl"
              value={formData.thumbnailUrl}
              onChange={handleInputChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://example.com/thumbnail.jpg"
            />
          </div>

          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700">
              Duration (seconds)
            </label>
            <input
              type="number"
              id="duration"
              name="duration"
              value={formData.duration}
              onChange={handleInputChange}
              min="0"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., 300 for 5 minutes"
            />
          </div>
        </div>
      </div>

      {/* Tags */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
        
        <div className="space-y-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagInputKeyPress}
              className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter a tag and press Enter"
            />
            <button
              type="button"
              onClick={handleAddTag}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4" />
            </button>
          </div>

          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Status</h3>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            name="isActive"
            checked={formData.isActive}
            onChange={handleInputChange}
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
            Active (visible to users)
          </label>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          disabled={loading}
        >
          {loading ? 'Saving...' : videoBlog ? 'Update Video Blog' : 'Create Video Blog'}
        </button>
      </div>
    </form>
  );
}
