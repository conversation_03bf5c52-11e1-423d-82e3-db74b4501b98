'use client';

import { useState,useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

export default function Login() {
  const{login,isLoading,error}=useAuth();
  // const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    // rememberMe: false,
  });

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.email || !formData.password) {
      toast.error("Please fill in all fields");
      return;
    }

    await login(formData.email, formData.password);
  };

  return (
    <div className='flex flex-col items-center justify-center min-h-screen px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8' style={{ backgroundColor: 'var(--background)' }}>
      <div className='w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl'>
        {/* Logo/Brand Section */}
        <div className='text-center mb-6 sm:mb-8'>
          <div className='inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg sm:rounded-xl mb-3 sm:mb-4'>
            <span className='text-xl sm:text-2xl font-bold text-white'>CWA</span>
          </div>
          <h1 className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-1 sm:mb-2' style={{ color: 'var(--foreground)' }}>CWA Admin</h1>
          <p className='text-sm sm:text-base lg:text-lg text-slate-600'>Welcome back to your dashboard</p>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4 sm:space-y-6 rounded-xl sm:rounded-2xl shadow-custom-lg py-6 sm:py-8 lg:py-12 px-4 sm:px-6 lg:px-10 bg-white'>
          <div className='space-y-2 sm:space-y-3'>
            <label htmlFor='email' className='block text-sm font-semibold text-gray-700'>
              Email Address
            </label>
            <input
              type='email'
              id='email'
              name='email'
              value={formData.email}
              onChange={handleChange}
              className='w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base transition-all duration-200 bg-gray-50 focus:bg-white'
              placeholder='Enter your email address'
              disabled={isLoading}
            />
          </div>
          <div className='space-y-2 sm:space-y-3'>
            <label htmlFor='password' className='block text-sm font-semibold text-gray-700'>
              Password
            </label>
            <input
              type='password'
              id='password'
              name='password'
              value={formData.password}
              onChange={handleChange}
              className='w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base transition-all duration-200 bg-gray-50 focus:bg-white'
              placeholder='Enter your password'
              disabled={isLoading}
            />
          </div>
          <div className='pt-2'>
            <button
              type='submit'
              disabled={isLoading}
              className='w-full px-4 sm:px-6 font-semibold py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-200 shadow-lg'
            >
              {isLoading ? (
                <span className='flex items-center justify-center'>
                  <svg className='animate-spin -ml-1 mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                    <circle className='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' strokeWidth='4'></circle>
                    <path className='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                  </svg>
                  Logging in...
                </span>
              ) : (
                'Login'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
