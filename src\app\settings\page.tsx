import DashboardLayout from '@/components/layout/DashboardLayout';
import { CogIcon, EnvelopeIcon, GlobeAltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';

export default function Settings() {
  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-white p-6 rounded-xl shadow-custom">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CogIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
              <p className="text-gray-600">Manage your application configuration and preferences</p>
            </div>
          </div>
        </div>

        {/* General Settings */}
        <div className="bg-white rounded-xl shadow-custom overflow-hidden">
          <div className="px-6 py-4 border-b" style={{ borderColor: 'var(--border-color)' }}>
            <div className="flex items-center space-x-3">
              <GlobeAltIcon className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">General Settings</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="siteName" className="block text-sm font-semibold text-gray-700 mb-2">
                  Site Name
                </label>
                <input
                  type="text"
                  id="siteName"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  defaultValue="CWA Admin Dashboard"
                />
              </div>
              <div>
                <label htmlFor="siteUrl" className="block text-sm font-semibold text-gray-700 mb-2">
                  Site URL
                </label>
                <input
                  type="text"
                  id="siteUrl"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  defaultValue="https://cwa-admin.example.com"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Email Settings */}
        <div className="bg-white rounded-xl shadow-custom overflow-hidden">
          <div className="px-6 py-4 border-b" style={{ borderColor: 'var(--border-color)' }}>
            <div className="flex items-center space-x-3">
              <EnvelopeIcon className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">Email Settings</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label htmlFor="smtpServer" className="block text-sm font-semibold text-gray-700 mb-2">
                  SMTP Server
                </label>
                <input
                  type="text"
                  id="smtpServer"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  defaultValue="smtp.example.com"
                />
              </div>
              <div>
                <label htmlFor="smtpPort" className="block text-sm font-semibold text-gray-700 mb-2">
                  SMTP Port
                </label>
                <input
                  type="text"
                  id="smtpPort"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  defaultValue="587"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white rounded-xl shadow-custom overflow-hidden">
          <div className="px-6 py-4 border-b" style={{ borderColor: 'var(--border-color)' }}>
            <div className="flex items-center space-x-3">
              <ShieldCheckIcon className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">Two-Factor Authentication</h4>
                  <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                </div>
                <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <span className="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                </button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">Session Timeout</h4>
                  <p className="text-sm text-gray-600">Automatically log out inactive users</p>
                </div>
                <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="30">30 minutes</option>
                  <option value="60">1 hour</option>
                  <option value="120">2 hours</option>
                  <option value="480">8 hours</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="button"
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-hover flex items-center gap-2 transition-colors shadow-custom font-semibold"
          >
            Save Settings
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
}
