'use client';

import { useEffect, useState, useRef } from 'react';
import { Product } from '@/types/user';
import { formatCurrency, formatDate } from '@/lib/utils';
import { XMarkIcon, TagIcon, CubeIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { IconButton } from '@/components/ui/Button';
import Card, { CardContent, CardHeader } from '@/components/ui/Card';
import ImageGallery from '@/components/ui/ImageGallery';

interface ProductDetailModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
}

export default function ProductDetailModal({ product, isOpen, onClose }: ProductDetailModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Handle escape key, body scroll, and focus management
  useEffect(() => {
    if (isOpen) {
      // Store the previously focused element
      previousActiveElement.current = document.activeElement as HTMLElement;

      // Prevent body scroll
      document.body.style.overflow = 'hidden';

      // Focus the close button after modal opens
      const focusTimer = setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);

      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };

      // Handle focus trap
      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key === 'Tab' && modalRef.current) {
          const focusableElements = modalRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              e.preventDefault();
              lastElement?.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              e.preventDefault();
              firstElement?.focus();
            }
          }
        }
      };

      document.addEventListener('keydown', handleEscape);
      document.addEventListener('keydown', handleTabKey);

      // Simulate loading state
      const loadingTimer = setTimeout(() => setIsLoading(false), 300);

      return () => {
        document.body.style.overflow = 'unset';
        document.removeEventListener('keydown', handleEscape);
        document.removeEventListener('keydown', handleTabKey);
        clearTimeout(focusTimer);
        clearTimeout(loadingTimer);

        // Restore focus to the previously focused element
        if (previousActiveElement.current) {
          previousActiveElement.current.focus();
        }
      };
    }
  }, [isOpen, onClose]);

  // Get product type styling
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'featured':
        return {
          label: 'Featured',
          className: 'bg-purple-100 text-purple-800 border-purple-200',
          icon: '⭐'
        };
      case 'sale':
        return {
          label: 'On Sale',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: '🏷️'
        };
      case 'new':
        return {
          label: 'New',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: '✨'
        };
      default:
        return {
          label: 'Regular',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: '📦'
        };
    }
  };

  // Get stock status styling
  const getStockConfig = (stock: boolean) => {
    return stock
      ? {
          label: 'In Stock',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: '✅'
        }
      : {
          label: 'Out of Stock',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: '❌'
        };
  };

  // Prepare images array for gallery
  const allImages = [product.image, ...(product.images || [])].filter(Boolean);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="product-modal-title"
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4 sm:p-6">
        <div
          ref={modalRef}
          className="relative w-full max-w-4xl transform transition-all duration-300 ease-out"
          role="document"
        >
          <Card padding="none" className="overflow-hidden">
            {/* Header */}
            <CardHeader className="flex flex-row items-center justify-between border-b border-gray-200 bg-gray-50/50">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <CubeIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h2
                    id="product-modal-title"
                    className="text-xl font-semibold text-gray-900"
                  >
                    Product Details
                  </h2>
                  <p className="text-sm text-gray-500">
                    View complete product information
                  </p>
                </div>
              </div>

              <IconButton
                ref={closeButtonRef}
                icon={<XMarkIcon className="w-5 h-5" />}
                onClick={onClose}
                aria-label="Close product details modal (Escape key)"
                className="hover:bg-gray-100"
              />
            </CardHeader>

            {/* Content */}
            <CardContent className="p-6">
              {isLoading ? (
                // Loading skeleton
                <div className="animate-pulse space-y-6">
                  <div className="flex flex-col lg:flex-row gap-6">
                    <div className="lg:w-1/2">
                      <div className="aspect-square bg-gray-200 rounded-xl"></div>
                    </div>
                    <div className="lg:w-1/2 space-y-4">
                      <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="space-y-2">
                        {[...Array(4)].map((_, i) => (
                          <div key={i} className="h-4 bg-gray-200 rounded"></div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Image Gallery Section */}
                    <div className="space-y-4">
                      <ImageGallery
                        images={allImages}
                        alt={product.name}
                        className="w-full"
                      />
                    </div>

                    {/* Product Information Section */}
                    <div className="space-y-6">
                      {/* Product Title and Badges */}
                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-gray-900 leading-tight">
                          {product.name}
                        </h3>

                        <div className="flex flex-wrap gap-2" role="group" aria-label="Product badges">
                          {/* Product Type Badge */}
                          <span
                            className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium border ${getTypeConfig(product.type).className}`}
                            role="status"
                            aria-label={`Product type: ${getTypeConfig(product.type).label}`}
                          >
                            <span className="text-base" aria-hidden="true">{getTypeConfig(product.type).icon}</span>
                            {getTypeConfig(product.type).label}
                          </span>

                          {/* Stock Status Badge */}
                          <span
                            className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium border ${getStockConfig(product.stock).className}`}
                            role="status"
                            aria-label={`Stock status: ${getStockConfig(product.stock).label}`}
                          >
                            <span className="text-base" aria-hidden="true">{getStockConfig(product.stock).icon}</span>
                            {getStockConfig(product.stock).label}
                          </span>

                          {/* Discount Badge */}
                          {product.discount && (
                            <span
                              className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium border bg-orange-100 text-orange-800 border-orange-200"
                              role="status"
                              aria-label={`Discount: ${product.discount}% off`}
                            >
                              <span className="text-base" aria-hidden="true">🏷️</span>
                              {product.discount}% OFF
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Price Section */}
                      <div
                        className="bg-gray-50 rounded-xl p-4"
                        role="region"
                        aria-labelledby="price-section"
                      >
                        <h4 id="price-section" className="sr-only">Product Pricing</h4>
                        <div className="flex items-baseline gap-3">
                          <span
                            className="text-3xl font-bold text-gray-900"
                            aria-label={`Current price: ${formatCurrency(product.price)}`}
                          >
                            {formatCurrency(product.price)}
                          </span>
                          {product.discount && (
                            <span
                              className="text-lg text-gray-500 line-through"
                              aria-label={`Original price: ${formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}`}
                            >
                              {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}
                            </span>
                          )}
                        </div>
                        {product.discount && (
                          <p
                            className="text-sm text-green-600 font-medium mt-1"
                            aria-label={`Savings: ${formatCurrency(product.price / (1 - parseFloat(product.discount) / 100) - product.price)}`}
                          >
                            You save {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100) - product.price)}
                          </p>
                        )}
                      </div>

                      {/* Product Details Grid */}
                      <div
                        className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                        role="group"
                        aria-labelledby="product-details-heading"
                      >
                        <h4 id="product-details-heading" className="sr-only">Product Details</h4>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-4"
                          role="group"
                          aria-labelledby="category-label"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <TagIcon className="w-4 h-4 text-gray-500" aria-hidden="true" />
                            <span id="category-label" className="text-sm font-medium text-gray-700">Category</span>
                          </div>
                          <p className="text-gray-900 font-medium" aria-describedby="category-label">
                            {product.category}
                          </p>
                        </div>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-4"
                          role="group"
                          aria-labelledby="quantity-label"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <CubeIcon className="w-4 h-4 text-gray-500" aria-hidden="true" />
                            <span id="quantity-label" className="text-sm font-medium text-gray-700">Quantity</span>
                          </div>
                          <p className="text-gray-900 font-medium" aria-describedby="quantity-label">
                            {product.quantity} units
                          </p>
                        </div>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-4 sm:col-span-2"
                          role="group"
                          aria-labelledby="created-label"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <CalendarIcon className="w-4 h-4 text-gray-500" aria-hidden="true" />
                            <span id="created-label" className="text-sm font-medium text-gray-700">Created</span>
                          </div>
                          <p className="text-gray-900 font-medium" aria-describedby="created-label">
                            <time dateTime={product.createdAt}>
                              {formatDate(new Date(product.createdAt))}
                            </time>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Description Section */}
                  {product.description && (
                    <section
                      className="border-t border-gray-200 pt-6"
                      aria-labelledby="description-heading"
                    >
                      <h4
                        id="description-heading"
                        className="text-lg font-semibold text-gray-900 mb-3"
                      >
                        Product Description
                      </h4>
                      <div className="bg-gray-50 rounded-xl p-4">
                        <p
                          className="text-gray-700 leading-relaxed whitespace-pre-wrap"
                          aria-describedby="description-heading"
                        >
                          {product.description}
                        </p>
                      </div>
                    </section>
                  )}
                </div>
              )}
            </CardContent>

            {/* Footer */}
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50/50">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-2.5 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Close
                </button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
