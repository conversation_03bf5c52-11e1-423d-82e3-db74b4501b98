interface OrderStatusBadgeProps {
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
}

export default function OrderStatusBadge({ status }: OrderStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: '⏳'
        };
      case 'processing':
        return {
          label: 'Processing',
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: '⚙️'
        };
      case 'shipped':
        return {
          label: 'Shipped',
          className: 'bg-purple-100 text-purple-800 border-purple-200',
          icon: '🚚'
        };
      case 'delivered':
        return {
          label: 'Delivered',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: '✅'
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: '❌'
        };
      default:
        return {
          label: 'Unknown',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: '❓'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium border ${config.className}`}>
      <span className="text-sm">{config.icon}</span>
      {config.label}
    </span>
  );
}
