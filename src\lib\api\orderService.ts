import apiClient from '../../../lib/api/apiClient';
import { Order, OrdersResponse, OrderFilters } from '@/types/order';

class OrderService {
  /**
   * Get all orders with optional filters
   */
  async getOrders(filters: OrderFilters = {}): Promise<OrdersResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.status) {
        params.append('status', filters.status);
      }
      if (filters.dateFilter) {
        params.append('dateFilter', filters.dateFilter);
      }
      if (filters.user) {
        params.append('user', filters.user);
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString());
      }

      const queryString = params.toString();
      const url = `/orders${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiClient.get<OrdersResponse>(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  /**
   * Get a single order by ID
   */
  async getOrder(id: string): Promise<{ status: string; data: { order: Order } }> {
    try {
      const response = await apiClient.get<{ status: string; data: { order: Order } }>(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(id: string, status: string): Promise<{ status: string; data: { order: Order } }> {
    try {
      const response = await apiClient.patch<{ status: string; data: { order: Order } }>(`/orders/${id}`, { status });
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  /**
   * Delete an order
   */
  async deleteOrder(id: string): Promise<{ status: string; message: string }> {
    try {
      // Get token from localStorage for authentication
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use fetch directly for consistent authorization handling
      const response = await fetch(`/api/orders/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete order' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      // Handle 204 No Content response
      if (response.status === 204) {
        return { status: 'success', message: 'Order deleted successfully' };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error deleting order:', error);
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    pendingOrders: number;
    processingOrders: number;
    shippedOrders: number;
    deliveredOrders: number;
    cancelledOrders: number;
  }> {
    try {
      // This would typically be a separate endpoint, but for now we'll calculate from all orders
      const response = await this.getOrders({ limit: 1000 }); // Get a large number to calculate stats
      const orders = response.data.orders;
      
      const stats = {
        totalOrders: orders.length,
        pendingOrders: orders.filter(order => order.status === 'pending').length,
        processingOrders: orders.filter(order => order.status === 'processing').length,
        shippedOrders: orders.filter(order => order.status === 'shipped').length,
        deliveredOrders: orders.filter(order => order.status === 'delivered').length,
        cancelledOrders: orders.filter(order => order.status === 'cancelled').length,
        totalRevenue: orders.reduce((sum, order) => sum + order.total, 0),
      };
      
      return stats;
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }
  }
}

export const orderService = new OrderService();
export default orderService;
